"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Crown } from "lucide-react"
import { cn } from "@/lib/utils"
import { useRouter } from "next/navigation"

interface UpgradeButtonProps {
  className?: string
  showText?: boolean
}

export function UpgradeButton({ className, showText = true }: UpgradeButtonProps) {
  const router = useRouter()

  const handleUpgrade = () => {
    router.push("/settings?tab=billing")
  }

  return (
    <Button
      onClick={handleUpgrade}
      className={cn(
        // Base styling
        "relative overflow-hidden",
        // Colors with gradient effect (same as floating AI button)
        "bg-gradient-to-br from-[#00796B] to-[#004D40]",
        "hover:from-[#00695C] hover:to-[#00332A]",
        "border-2 border-[#FFD54F]/20",
        // Shadow and glow effects
        "shadow-lg shadow-[#00796B]/25",
        "hover:shadow-xl hover:shadow-[#00796B]/40",
        // Shine effect
        "before:absolute before:inset-0",
        "before:bg-gradient-to-br before:from-white/20 before:via-transparent before:to-transparent",
        "before:opacity-0 hover:before:opacity-100",
        "before:transition-opacity before:duration-300",
        // Touch-friendly sizing for mobile
        "min-h-[44px] min-w-[44px]",
        // Smooth transitions
        "transition-all duration-300 ease-in-out",
        "hover:scale-105 active:scale-95",
        // Focus styles - remove yellow outline on click
        "focus:outline-none focus:ring-0 focus:ring-offset-0",
        // Text color
        "text-white hover:text-white",
        className
      )}
      size={showText ? "sm" : "icon"}
      aria-label="Upgrade to Pro"
    >
      <Crown className="h-4 w-4" style={{ filter: "drop-shadow(0 0 4px rgba(255, 213, 79, 0.6))" }} />
      {showText && <span className="ml-2 font-medium">Upgrade</span>}
    </Button>
  )
}
